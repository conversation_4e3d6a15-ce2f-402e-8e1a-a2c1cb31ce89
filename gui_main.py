import sys
import numpy as npe
    QApplication,
    QFileDialog,
    QPushButton,
    QHBoxLayout,
    QComboBox,
    QSplitter,
    QStatusBar,
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from onnxocr.onnx_paddleocr import ONNXPaddleOcr, sav2Img

    """OCR处理线程，避免界面卡顿"""
    progress = pyqtSignal(int)

        super().__init__()
        self.use_gpu = use_gpu
    def run(self):
            # 初始化OCR模型
            
            start_time = time.time()
            end_time = time.time()
            # 发送结果和处理时间
        except Exception as e:

class OCRApp(QMainWindow):
        super().__init__()
        self.current_image = None
        self.result_image = None
    def initUI(self):
        self.setWindowTitle("ONNX OCR 工具")

        self.statusBar = QStatusBar()
        
        self.progressBar = QProgressBar()
        self.progressBar.setTextVisible(True)
        self.progressBar.hide()
        # 中心部件
        self.setCentralWidget(central_widget)
        
        splitter = QSplitter(Qt.Horizontal)
        # 左侧图像显示区域
        image_layout = QVBoxLayout()
        self.image_label = QLabel("请选择图片")
        self.image_label.setMinimumSize(400, 300)
        image_layout.addWidget(self.image_label)
        image_widget.setLayout(image_layout)
        # 右侧结果显示区域
        result_layout = QVBoxLayout()
        result_label = QLabel("识别结果")
        result_layout.addWidget(result_label)
        self.result_text = QTextEdit()
        result_layout.addWidget(self.result_text)
        result_widget.setLayout(result_layout)
        # 添加到分割器
        splitter.addWidget(result_widget)
        
        
        control_layout = QHBoxLayout()
        # 按钮区域
        btn_load.clicked.connect(self.load_image)

        model_label = QLabel("选择模型:")
        
        self.model_combo.addItems(["PP-OCRv5", "PP-OCRv4"])
        
        self.use_gpu_checkbox = QComboBox()
        control_layout.addWidget(self.use_gpu_checkbox)
        # OCR按钮
        btn_recognize.clicked.connect(self.run_ocr)
        
        btn_save = QPushButton("保存结果")
        control_layout.addWidget(btn_save)
        main_layout.addLayout(control_layout)
        central_widget.setLayout(main_layout)
    def load_image(self):
        file_path, _ = QFileDialog.getOpenFileName(
        )
            try:
                if self.current_image is None:
                    return
                self.statusBar.showMessage(f"已加载图片: {file_path}")
                self.result_text.clear()
                self.result_image = None
                QMessageBox.warning(self, "错误", f"加载图片时出错: {str(e)}")
    def show_image(self, image):
        if image is None:
            
        bytes_per_line = ch * w
            image.data, w, h, bytes_per_line, QImage.Format_RGB888
        
        
        label_width = self.image_label.width()
        label_height = self.image_label.height()
        if pixmap.width() > label_width or pixmap.height() > label_height:
                Qt.KeepAspectRatio, 
            )
        self.image_label.setPixmap(pixmap)
defrun_ocr(self):
        if self.current_image is None:
            return
        # 显示进度条
        self.progressBar.show().progressBar.setValue(0)
        
        # 获取是否使用GPU
        # 创建并启动工作线程
        self.worker.finished.connect(self.process_result)self.worker = OCRWorker(self.current_image, use_gpu)
        self.worker.error.connect(self.handle_error)

        self.ocr_result = result
        # 在图像上绘制结果
        self.result_image = sav2Img(self.result_image, result, name=None)
        # 显示带标记的图像
        
        result_text = f"识别完成，耗时: {processing_time:.3f}秒\n\n"
        if result and len(result) > 0:
                if isinstance(line[1], tuple) and len(line[1]) >= 2:
                    result_text += f"{i+1}. {text} (置信度: {confidence:.4f})\n"
            result_text += "未识别到文字"
        self.result_text.setText(result_text)
        # 隐藏进度条，更新状态
        self.statusBar.showMessage(f"OCR识别完成，耗时: {processing_time:.3f}秒")

        self.progressBar.hide()
        QMessageBox.critical(self, "错误", f"OCR处理失败: {error_msg}")
    def save_result(self):
        if self.ocr_result is None:
            return
        # 保存文本结果
            self, "保存文本结果", "", "文本文件 (*.txt)"
        
            try:
                    for line in self.ocr_result[0]:
                            text, confidence = line[1][0], line[1][1]
                
                if self.result_image is not None:
                        self, '保存图像', 
                        QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
                    
                        image_path, _ = QFileDialog.getSaveFileName(
                        )
                            cv2.imwrite(image_path, self.result_image)
                            return
                self.statusBar.showMessage(f"文本结果已保存至 {text_path}")
                QMessageBox.critical(self, "错误", f"保存结果失败: {str(e)}")

    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    ex.show()