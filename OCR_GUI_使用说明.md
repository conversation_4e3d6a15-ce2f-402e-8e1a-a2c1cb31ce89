# OnnxOCR GUI 使用说明

## 📋 概述

这是一个基于tkinter的图形化OCR（光学字符识别）应用程序，使用OnnxOCR引擎进行文字识别。支持多种图片格式，能够识别中文、英文、日文等多种语言的文字。

## 🚀 快速开始

### 方法1：使用批处理文件启动
双击 `start_ocr_gui.bat` 文件即可启动GUI应用。

### 方法2：命令行启动
```bash
# 激活虚拟环境（如果有）
venv\Scripts\activate

# 启动GUI应用
python ocr_gui.py
```

## 🎯 主要功能

### 1. 图片选择和加载
- **选择图片**：点击"选择图片"按钮，支持以下格式：
  - JPEG文件 (*.jpg, *.jpeg)
  - PNG文件 (*.png)
  - BMP文件 (*.bmp)
  - TIFF文件 (*.tiff, *.tif)
- **图片显示**：选择的图片会自动显示在右侧画布中
- **自动缩放**：图片会自动调整大小以适应显示区域

### 2. OCR文字识别
- **开始识别**：选择图片后，点击"开始识别"按钮
- **实时进度**：显示识别进度和状态信息
- **多线程处理**：识别过程在后台进行，不会阻塞界面

### 3. 识别结果显示
- **文本结果**：在底部文本框中显示详细的识别结果
- **可视化结果**：在图片上绘制识别到的文字区域和边界框
- **置信度显示**：显示每个识别区域的置信度分数

### 4. 结果保存
- **保存文本**：将识别结果保存为文本文件
- **包含详细信息**：保存的文件包含文字内容、置信度和坐标信息

### 5. 设置选项
- **GPU加速**：可选择是否使用GPU进行加速（需要CUDA环境）
- **角度分类**：可选择是否启用文字角度分类功能

## 🖥️ 界面说明

### 左侧控制面板
- **选择图片**：打开文件选择对话框
- **开始识别**：执行OCR识别（需要先选择图片）
- **保存结果**：保存识别结果到文本文件
- **清除**：清除所有内容，重置界面
- **设置选项**：
  - 使用GPU：启用/禁用GPU加速
  - 角度分类：启用/禁用文字角度分类
- **状态显示**：显示当前操作状态和进度

### 右侧显示区域
- **图片显示区**：显示选择的图片和识别结果
- **识别结果区**：显示详细的文字识别结果

## 📝 使用步骤

1. **启动应用**
   - 双击 `start_ocr_gui.bat` 或运行 `python ocr_gui.py`
   - 等待OCR模型初始化完成

2. **选择图片**
   - 点击"选择图片"按钮
   - 在文件对话框中选择要识别的图片文件
   - 图片会自动加载并显示

3. **配置设置**（可选）
   - 根据需要调整GPU和角度分类设置
   - 首次使用建议保持默认设置

4. **开始识别**
   - 点击"开始识别"按钮
   - 等待识别完成（状态栏会显示进度）

5. **查看结果**
   - 识别完成后，图片上会显示文字边界框
   - 底部文本框会显示详细的识别结果

6. **保存结果**（可选）
   - 点击"保存结果"按钮
   - 选择保存位置和文件名
   - 结果会保存为UTF-8编码的文本文件

## ⚠️ 注意事项

### 系统要求
- Python 3.6 或更高版本
- 已安装所需依赖包（见 requirements.txt）
- 足够的内存（建议4GB以上）

### 性能优化
- **GPU加速**：如果有NVIDIA GPU和CUDA环境，启用GPU可显著提升识别速度
- **图片大小**：过大的图片会影响识别速度，建议控制在合理范围内
- **内存使用**：处理大图片时可能占用较多内存

### 识别效果
- **图片质量**：清晰、对比度高的图片识别效果更好
- **文字大小**：过小的文字可能影响识别准确率
- **背景复杂度**：简单背景的图片识别效果更佳

## 🔧 故障排除

### 常见问题

1. **模型初始化失败**
   - 检查模型文件是否存在于 `onnxocr/models/` 目录
   - 确认依赖包已正确安装

2. **GPU相关错误**
   - 如果出现GPU错误，可以禁用GPU选项使用CPU模式
   - 检查CUDA和cuDNN环境配置

3. **图片加载失败**
   - 确认图片文件格式受支持
   - 检查图片文件是否损坏

4. **识别结果为空**
   - 检查图片中是否包含清晰的文字
   - 尝试调整图片质量或大小

### 获取帮助
如果遇到问题，可以：
1. 查看控制台输出的错误信息
2. 检查状态栏显示的错误提示
3. 参考项目的GitHub页面获取更多帮助

## 📊 测试功能

项目包含一个测试脚本 `test_gui_ocr.py`，可以用来验证OCR功能是否正常：

```bash
python test_gui_ocr.py
```

测试脚本会：
- 列出可用的测试图片
- 执行OCR识别测试
- 生成带有识别结果的图片
- 报告测试结果

## 🎉 功能特色

- ✅ **用户友好**：直观的图形界面，操作简单
- ✅ **高性能**：基于ONNX的高效OCR引擎
- ✅ **多语言支持**：支持中文、英文、日文等多种语言
- ✅ **实时反馈**：显示识别进度和状态信息
- ✅ **结果可视化**：在图片上直接显示识别结果
- ✅ **灵活配置**：支持GPU加速和各种参数调整
- ✅ **结果保存**：方便的结果导出功能

享受使用OnnxOCR GUI进行文字识别吧！🚀
