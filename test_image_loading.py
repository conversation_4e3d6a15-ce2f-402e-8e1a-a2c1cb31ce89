#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片加载功能的脚本
用于验证各种图片文件的加载情况
"""

import os
import cv2
import numpy as np
from PIL import Image

def test_image_loading():
    """测试图片加载功能"""
    print("🔍 测试图片加载功能...")
    
    # 测试图片目录
    test_dir = "onnxocr/test_images"
    
    if not os.path.exists(test_dir):
        print(f"❌ 测试目录不存在: {test_dir}")
        return False
    
    # 获取所有图片文件
    image_files = []
    for file in os.listdir(test_dir):
        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')):
            image_files.append(os.path.join(test_dir, file))
    
    if not image_files:
        print("❌ 没有找到测试图片")
        return False
    
    print(f"📸 找到 {len(image_files)} 个图片文件")
    
    success_count = 0
    error_count = 0
    
    for i, file_path in enumerate(image_files[:10]):  # 只测试前10个文件
        print(f"\n测试 {i+1}/{min(10, len(image_files))}: {os.path.basename(file_path)}")
        
        try:
            # 检查文件基本信息
            if not os.path.exists(file_path):
                print("  ❌ 文件不存在")
                error_count += 1
                continue
            
            file_size = os.path.getsize(file_path)
            print(f"  📏 文件大小: {file_size} 字节")
            
            if file_size == 0:
                print("  ❌ 文件为空")
                error_count += 1
                continue
            
            # 测试OpenCV读取
            try:
                # 处理中文路径
                if any(ord(char) > 127 for char in file_path):
                    print("  🔤 检测到中文路径，使用特殊方法读取")
                    with open(file_path, 'rb') as f:
                        image_data = np.frombuffer(f.read(), np.uint8)
                    image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
                else:
                    image = cv2.imread(file_path)
                
                if image is None:
                    print("  ❌ OpenCV无法读取图片")
                    error_count += 1
                    continue
                
                height, width = image.shape[:2]
                print(f"  ✅ 成功读取，尺寸: {width}x{height}")
                
                # 测试PIL读取
                try:
                    pil_image = Image.open(file_path)
                    print(f"  ✅ PIL也能读取，格式: {pil_image.format}")
                except Exception as pil_error:
                    print(f"  ⚠️  PIL读取失败: {str(pil_error)}")
                
                success_count += 1
                
            except Exception as cv_error:
                print(f"  ❌ OpenCV读取错误: {str(cv_error)}")
                error_count += 1
                
        except Exception as e:
            print(f"  ❌ 测试过程出错: {str(e)}")
            error_count += 1
    
    print(f"\n📊 测试结果:")
    print(f"  ✅ 成功: {success_count}")
    print(f"  ❌ 失败: {error_count}")
    print(f"  📈 成功率: {success_count/(success_count+error_count)*100:.1f}%")
    
    return success_count > 0

def create_test_images():
    """创建一些测试图片"""
    print("\n🎨 创建测试图片...")
    
    test_dir = "test_images_temp"
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        # 创建一个简单的测试图片
        import numpy as np
        
        # 创建一个彩色图片
        image = np.zeros((200, 300, 3), dtype=np.uint8)
        image[:, :, 0] = 255  # 红色通道
        image[50:150, 50:250, 1] = 255  # 绿色矩形
        image[75:125, 75:225, 2] = 255  # 蓝色矩形
        
        test_file = os.path.join(test_dir, "test_image.jpg")
        cv2.imwrite(test_file, image)
        print(f"  ✅ 创建测试图片: {test_file}")
        
        # 测试读取
        loaded_image = cv2.imread(test_file)
        if loaded_image is not None:
            print("  ✅ 测试图片读取成功")
            return test_file
        else:
            print("  ❌ 测试图片读取失败")
            return None
            
    except Exception as e:
        print(f"  ❌ 创建测试图片失败: {str(e)}")
        return None

def test_chinese_path():
    """测试中文路径处理"""
    print("\n🔤 测试中文路径处理...")
    
    # 创建包含中文的测试目录
    chinese_dir = "测试图片"
    try:
        os.makedirs(chinese_dir, exist_ok=True)
        
        # 复制一个测试图片到中文目录
        source_files = []
        test_dir = "onnxocr/test_images"
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if file.lower().endswith('.jpg'):
                    source_files.append(os.path.join(test_dir, file))
                    break
        
        if source_files:
            import shutil
            chinese_file = os.path.join(chinese_dir, "中文测试图片.jpg")
            shutil.copy2(source_files[0], chinese_file)
            print(f"  📄 创建中文路径测试文件: {chinese_file}")
            
            # 测试读取
            try:
                with open(chinese_file, 'rb') as f:
                    image_data = np.frombuffer(f.read(), np.uint8)
                image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
                
                if image is not None:
                    print("  ✅ 中文路径图片读取成功")
                    return True
                else:
                    print("  ❌ 中文路径图片读取失败")
                    return False
                    
            except Exception as e:
                print(f"  ❌ 中文路径测试失败: {str(e)}")
                return False
        else:
            print("  ⚠️  没有找到源测试图片")
            return False
            
    except Exception as e:
        print(f"  ❌ 中文路径测试出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 图片加载功能测试")
    print("=" * 60)
    
    # 测试现有图片
    test1_result = test_image_loading()
    
    # 创建并测试新图片
    test_file = create_test_images()
    
    # 测试中文路径
    test3_result = test_chinese_path()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"  现有图片测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  新建图片测试: {'✅ 通过' if test_file else '❌ 失败'}")
    print(f"  中文路径测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result or test_file:
        print("\n🎉 图片加载功能基本正常，GUI应用应该可以正常加载图片")
    else:
        print("\n⚠️  图片加载功能存在问题，请检查环境配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
