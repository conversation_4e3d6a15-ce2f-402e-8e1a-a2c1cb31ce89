import numpy as np
from PIL import Image


def print_pixel_matrix(image_path):
    """打印图片的像素矩阵"""
    # 打开图片并转换为灰度
    img = Image.open(image_path).convert("L")

    # 转换为numpy数组
    img_array = np.array(img)

    print(f"=== 图片分析: {image_path} ===")
    print(f"尺寸: {img.size}")
    print(f"模式: {img.mode}")
    print("\n像素值分布:")

    # 计算直方图
    hist = np.histogram(img_array, bins=10, range=(0, 255))[0]
    for i, count in enumerate(hist):
        start = i * 25.5
        end = (i + 1) * 25.5
        print(f"值范围 {start:3.0f}-{end:3.0f}: {count:5d} 像素")

    # 打印简化的像素矩阵（每个像素用一个字符表示）
    print("\n简化像素矩阵 (暗->亮: .,:;+*#@):")

    # 创建映射函数
    def pixel_to_char(p):
        chars = " .,:;+*#@"
        return chars[min(int(p * len(chars) / 256), len(chars) - 1)]

    # 为了使输出更易读，我们可能需要跳过一些像素
    step = max(1, min(img_array.shape[0], img_array.shape[1]) // 40)

    for i in range(0, img_array.shape[0], step):
        line = ""
        for j in range(0, img_array.shape[1], step):
            line += pixel_to_char(img_array[i, j])
        print(line)

    # 保存不同的处理版本
    print("\n保存处理后的图片...")

    # 保存灰度版本
    img.save("gray_version.png")
    print("灰度版本已保存为: gray_version.png")

    # 保存二值化版本（使用多个阈值）
    thresholds = [64, 128, 192]
    for threshold in thresholds:
        binary = img.point(lambda x: 255 if x > threshold else 0, "1")
        binary.save(f"binary_version_t{threshold}.png")
        print(f"二值化版本(阈值={threshold})已保存为: binary_version_t{threshold}.png")


if __name__ == "__main__":
    print_pixel_matrix("test.png")
    print("\n处理后的图片:")
    print_pixel_matrix("test_enhanced.png")
