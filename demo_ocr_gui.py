#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OnnxOCR GUI 演示脚本
展示如何使用OCR GUI进行文字识别
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'tkinter',
        'PIL',
        'cv2',
        'numpy',
        'onnxruntime'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            elif package == 'cv2':
                import cv2
            elif package == 'numpy':
                import numpy
            elif package == 'onnxruntime':
                import onnxruntime
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有依赖包已安装")
        return True

def check_models():
    """检查模型文件是否存在"""
    print("\n🔍 检查模型文件...")
    
    model_paths = [
        "onnxocr/models/ppocrv5/det_model",
        "onnxocr/models/ppocrv5/rec_model"
    ]
    
    all_exist = True
    for path in model_paths:
        if os.path.exists(path):
            print(f"  ✅ {path}")
        else:
            print(f"  ❌ {path}")
            all_exist = False
    
    if not all_exist:
        print("\n⚠️  部分模型文件缺失")
        print("请确保模型文件已正确下载到 onnxocr/models/ 目录")
        return False
    else:
        print("✅ 模型文件检查通过")
        return True

def list_test_images():
    """列出可用的测试图片"""
    print("\n📸 可用的测试图片:")
    
    test_dir = "onnxocr/test_images"
    if not os.path.exists(test_dir):
        print("  ❌ 测试图片目录不存在")
        return []
    
    image_files = []
    for file in os.listdir(test_dir):
        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
            image_files.append(os.path.join(test_dir, file))
            print(f"  📄 {file}")
    
    return image_files

def run_gui():
    """启动GUI应用"""
    print("\n🚀 启动OCR GUI应用...")
    
    try:
        # 启动GUI应用
        subprocess.Popen([sys.executable, "ocr_gui.py"])
        print("✅ GUI应用已启动")
        return True
    except Exception as e:
        print(f"❌ 启动GUI失败: {str(e)}")
        return False

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用提示:")
    print("1. 等待OCR模型初始化完成（状态栏显示'OCR模型初始化完成'）")
    print("2. 点击'选择图片'按钮，选择要识别的图片文件")
    print("3. 图片加载后，点击'开始识别'按钮进行OCR识别")
    print("4. 识别完成后，可以在图片上看到文字边界框")
    print("5. 底部文本框会显示详细的识别结果")
    print("6. 可以点击'保存结果'按钮将结果保存为文本文件")
    print("7. 使用'清除'按钮可以重置界面")
    
    print("\n⚙️  设置选项:")
    print("- 使用GPU: 如果有NVIDIA GPU和CUDA环境，可以启用此选项加速识别")
    print("- 角度分类: 启用此选项可以自动纠正文字的角度")
    
    print("\n📁 推荐测试图片:")
    test_images = [
        "onnxocr/test_images/715873facf064583b44ef28295126fa7.jpg",
        "onnxocr/test_images/1.jpg",
        "onnxocr/test_images/11.jpg"
    ]
    
    for img in test_images:
        if os.path.exists(img):
            print(f"  📄 {img}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 OnnxOCR GUI 演示程序")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装所需依赖包")
        return
    
    # 检查模型
    if not check_models():
        print("\n⚠️  模型文件检查失败，但仍可尝试运行")
    
    # 列出测试图片
    test_images = list_test_images()
    
    # 显示使用提示
    show_usage_tips()
    
    print("\n" + "=" * 60)
    
    # 询问是否启动GUI
    while True:
        choice = input("\n是否现在启动OCR GUI应用？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            if run_gui():
                print("\n🎉 GUI应用已启动！请在新窗口中使用OCR功能。")
                print("💡 提示：关闭GUI窗口即可退出应用。")
            break
        elif choice in ['n', 'no', '否']:
            print("\n👋 您可以稍后手动运行以下命令启动GUI:")
            print("python ocr_gui.py")
            print("或双击 start_ocr_gui.bat 文件")
            break
        else:
            print("请输入 y 或 n")
    
    print("\n📖 更多信息请查看 'OCR_GUI_使用说明.md' 文件")
    print("🔧 如果遇到问题，可以运行 'python test_gui_ocr.py' 进行功能测试")

if __name__ == "__main__":
    main()
