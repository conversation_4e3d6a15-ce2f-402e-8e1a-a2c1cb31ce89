# ONNX OCR 安装指南

## 环境要求

- Python 3.7 或更高版本
- 虚拟环境（推荐）
- Windows/Linux/MacOS

## 安装步骤

### 1. 创建并激活虚拟环境

```bash
# Windows
python -m venv venv
.\venv\Scripts\activate

# Linux/MacOS
python -m venv venv
source venv/bin/activate
```

### 2. 安装依赖

#### 方法一：使用 pip（推荐）

```bash
# 首先安装正确版本的 NumPy
pip install "numpy<2.0.0"

# 然后安装其他依赖
pip install -r requirements.txt
```

#### 方法二：使用 setup.py

```bash
pip install .
```

### 3. 验证安装

```bash
# 运行命令行工具
python ocr_tool.py onnxocr/test_images/1.jpg

# 或运行GUI界面
python ocr_gui.py
```

## 常见问题解决

### 1. NumPy 兼容性问题

如果遇到以下错误：
```
AttributeError: _ARRAY_API not found
ImportError: numpy.core.multiarray failed to import
```

解决方案：
```bash
# 1. 卸载当前的 NumPy
pip uninstall numpy

# 2. 安装兼容版本的 NumPy
pip install "numpy<2.0.0"
```

### 2. ONNX Runtime 问题

如果遇到 ONNX Runtime 相关错误，尝试：
```bash
# 卸载当前版本
pip uninstall onnxruntime onnxruntime-gpu

# 重新安装 CPU 版本
pip install onnxruntime

# 或者如果有 NVIDIA GPU，安装 GPU 版本
pip install onnxruntime-gpu
```

### 3. 找不到模型文件

确保模型文件位于正确的目录：
```
onnxocr/
└── models/
    └── ppocrv5/
        ├── det/
        │   └── det.onnx
        ├── cls/
        │   └── cls.onnx
        ├── rec/
        │   └── rec.onnx
        └── ppocrv5_dict.txt
```

### 4. GUI 界面问题

如果 GUI 界面无法启动：

1. 确认 tkinter 已安装：
```python
import tkinter
```

2. 对于 Linux 用户，可能需要安装额外的包：
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# Fedora
sudo dnf install python3-tkinter

# CentOS
sudo yum install python3-tkinter
```

## 开发环境设置

如果你想参与开发，还需要安装开发工具：

```bash
pip install -r requirements.txt

# 安装开发依赖
pip install pytest black flake8
```

## 更新说明

当更新到新版本时，建议：

1. 备份自定义配置
2. 卸载旧版本
3. 安装新版本
4. 恢复自定义配置

```bash
# 卸载旧版本
pip uninstall onnxocr

# 安装新版本
pip install .
```

## 支持

如果遇到其他问题，请：

1. 检查 [README.md](README.md) 文档
2. 查看项目 Issues 页面
3. 提交新的 Issue，并附上：
   - Python 版本信息
   - 完整的错误信息
   - 操作系统信息
   - 复现步骤