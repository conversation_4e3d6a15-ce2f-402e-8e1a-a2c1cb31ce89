<html lang="zh-CN">
    <aeta>chrsetUTF8
     title>ONNX OCR Web服务</titl meta name="viewport" content="width=device-width, initial-scale=1.0">
    e   body{
             in -hefgha: 1.6;y: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-wid h: 1000px; color: #333;
            padding: 20px; margin: 0 auto;
            background-color: #f5f5f5;
        .heatext-eli center
        }om: 30px;
            color: #2c3e50;
        }            margin-bottom: 10px;
            .ackgruunp-color:awdi-e;ainer {
            border-radius: 8px;
            padding: 30px;
        .ploafm{
            flex-direction: column;
        }
            margin-bottom: 15px;
        .form-group label {
            margin-bottom: 5px;
            color: #2c3e50;
        .form-group select {
            padding: 10px;
            border-radius: 4px;
        }
            position: relative;
            display: inline-block;
        }
            display: block;
            background-color: #f9f9f9;
            border-radius: 8px;
            cursor: pointer;
        }
            background-color: #edf7fd;
        }
            font-size: 48px;
            margin-bottom: 10px;
        }
            position: absolute;
            top: 0;
            width: 100%;
            cursor: pointer;
        .submit-btn {
            color: white;
            padding: 12px 20px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
        .submit-btn:hover {
        }
            background-color: #95a5a6;
        }
            margin-top: 20px;
            display: none;
        .preview-container img {
            max-height: 300px;
            border-radius: 4px;
        .features {
            flex-wrap: wrap;
            margin-bottom: 30px;
        .feature-card {
            min-width: 200px;
            border-radius: 8px;
            padding: 20px;
        }
            font-size: 36px;
            margin-bottom: 15px;
        .feature-card h3 {
            color: #2c3e50;
        .footer {
            margin-top: 30px;
            font-size: 0.9em;
        .loading {
            text-align: center;
        }
            border: 5px solid #f3f3f3;
            border-radius: 50%;
            height: 50px;
            margin: 0 auto 15px;
        @keyframes spin {
            100% { transform: rotate(360deg); }
    </style>
</head>
    <div class="header">
        <p>基于 ONNX 的高性能多语言 OCR 引擎</p>

        <div class="feature-card">
            <h3>多语言支持</h3>
        </div>
            <i class="fas fa-bolt"></i>
            <p>基于ONNX Runtime的高性能推理引擎，支持CPU和GPU</p>            <h3>高性能推理</h3>
        </div>
            <i class="fas fa-cloud"></i>
            <p>提供Web界面和API接口，方便集成到其他应用</p>

    <div class="upload-container">
                <label for="model">选择OCR模型:</label>            <div class="form-group">
                <select id="model" name="model">
               v</=ePPct4</option>
        <   
                <label for="use_gpu">选择推理设备:  label   <div class="form-group">
                    <option value="false">CPU</option>                <select id="use_gpu" name="use_gpu">
                </select    <option value="true">GPU</option>
           
                 label  l islafile-i=put-lfbel" foe="fnte-">
                     sp       fileLibssfa点击或拖拽图片到此处-cspoupload-alt"></i>
                 input type="file" i ="f le" name="file" class="file-input" accept="image/*"       </label>
                  </div>
            <div class="preview-container" id="previewContainer">
                </div>
            <button type="submit" class="submit-btn" id="submitBtn" disabled>开始识别</button>
    </div>
    <div class="loading" id="loadingContainer">
    </d v>
    <div class="footer">
        <p>© 2023 ONNX OCR 项目 | 基于 ONNX 的高性能多语言 OCR 引擎</p>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
                  smatePrev eweLadobumelt.gemElementByIdByimagePreview('fileLabel');
            const previewContainer = document.getElementById('previewContainer');
            const ocrForm = document.getElementById('ocrForm');
          // 文件选择事件
                if (this.files && this.files[0]) {            fileInput.addEventListener('change', function() {
                    fileL bel.text    snt fifiltsm
                    
                    reader.onload = function(e) {                    const reader = new FileReader();
                        previewContainer.style.display = 'block';             imagePreview.src = e.target.result;
                      ader re  AsDa}aUR;(fl;
                    // 启用提交按钮
                              submitBtn.disabled = false;
                        });
            // 表单提交事件
            });
            // 拖放功能
            
                e.preventDefault();
                this.style.borderColor = '#2980b9';
            
                e.preventDefault();
                this.style.borderColor = '#3498db';
            
                e.preventDefault();
                this.style.borderColor = '#3498db';
                if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                    const event = new Event('change');
                }
        });
</body>