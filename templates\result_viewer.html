<html lang="zh-CN">
    <metacarse="UTF-8"
    <title>ONNX OCR 结果查看器</title>
        body {
            line-height: 1.6;
            max-width: 1200px;
            padding: 20px;
        }
            text-align: center;
        }
            color: #2c3e50;
        }
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        .result-header {
            display: flex;
            border-bottom: 1px solid #eee;
            mprdiinbo-tooto15pxx;
        .result-header h2 {
            color: #3498db;
        .esult-contet{
            flex-wrap: wrap;
        }
            flex: 1;
        }
        .image-container img {
            border-radius: 4px;
        .text-container {
            min-width: 300px;
        .text-item {
            border-left: 3px solid #3498db;
            margin-bottom: 10px;
        .confidence {
            color: #7f8c8d;
        }
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
        }
            background-color: #2980b9;
        .footer {
            margin-top: 30px;
        }
    </style>
<body>
        <h1>ONNX OCR 结果查看器</h1>
    </div>
    <div class="result-container">
            <h2>识别结果</h2>
        </div>
            <div class="image-container">
            </div>
                {% for item in ocr_results %}
                    <div>{{item.text}}</div>
                </div>
            </div>
    </div>
    <div class="footer">
    </div>
    <script>
            // 下载逻辑
        });
</body>