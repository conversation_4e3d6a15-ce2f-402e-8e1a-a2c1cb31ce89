#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OCR GUI功能的简单脚本
"""

import cv2
import os
from onnxocr.onnx_paddleocr import ONNXPaddleOcr
from onnxocr.utils import draw_ocr

def test_ocr_basic():
    """测试基本OCR功能"""
    print("正在初始化OCR模型...")
    
    try:
        # 初始化OCR模型
        model = ONNXPaddleOcr(use_angle_cls=True, use_gpu=False)
        print("OCR模型初始化成功")
        
        # 测试图片路径
        test_image_path = "./onnxocr/test_images/715873facf064583b44ef28295126fa7.jpg"
        
        if not os.path.exists(test_image_path):
            print(f"测试图片不存在: {test_image_path}")
            # 尝试其他测试图片
            test_images = [
                "./onnxocr/test_images/1.jpg",
                "./onnxocr/test_images/11.jpg",
                "./onnxocr/test_images/12.jpg"
            ]
            
            for img_path in test_images:
                if os.path.exists(img_path):
                    test_image_path = img_path
                    break
            else:
                print("没有找到可用的测试图片")
                return False
        
        print(f"使用测试图片: {test_image_path}")
        
        # 读取图片
        image = cv2.imread(test_image_path)
        if image is None:
            print("无法读取测试图片")
            return False
        
        print("正在执行OCR识别...")
        
        # 执行OCR识别
        result = model.ocr(image)
        
        if result and result[0]:
            print(f"识别成功！识别到 {len(result[0])} 个文本区域")
            
            # 显示识别结果
            for i, line in enumerate(result[0]):
                text = line[1][0]
                confidence = line[1][1]
                print(f"区域 {i+1}: {text} (置信度: {confidence:.4f})")
            
            # 测试绘制功能
            print("正在测试绘制功能...")
            boxes = [line[0] for line in result[0]]
            texts = [line[1][0] for line in result[0]]
            scores = [line[1][1] for line in result[0]]
            
            # 转换为RGB格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 绘制结果
            result_image = draw_ocr(image_rgb, boxes, texts, scores)
            
            # 保存结果图片
            output_path = "test_ocr_result.jpg"
            cv2.imwrite(output_path, cv2.cvtColor(result_image, cv2.COLOR_RGB2BGR))
            print(f"结果图片已保存到: {output_path}")
            
            return True
        else:
            print("未识别到任何文字")
            return False
            
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        return False

def list_test_images():
    """列出可用的测试图片"""
    test_dir = "./onnxocr/test_images"
    if os.path.exists(test_dir):
        print("可用的测试图片:")
        for file in os.listdir(test_dir):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                print(f"  - {file}")
    else:
        print("测试图片目录不存在")

if __name__ == "__main__":
    print("=" * 50)
    print("OnnxOCR GUI 功能测试")
    print("=" * 50)
    
    # 列出测试图片
    list_test_images()
    print()
    
    # 测试基本OCR功能
    success = test_ocr_basic()
    
    print()
    print("=" * 50)
    if success:
        print("✅ OCR功能测试通过！GUI应用应该可以正常工作。")
    else:
        print("❌ OCR功能测试失败，请检查环境配置。")
    print("=" * 50)
