# GUI界面更新说明

## 🎯 更新概览

根据您的需求，我已经对OCR GUI界面进行了全面的重新设计和优化，主要改进包括：

### ✅ 主要改进

1. **图片显示窗口成为主体**
   - 图片显示区域现在占据界面的主要空间
   - 控制面板缩小到左侧，不再占用过多空间
   - 界面比例调整为 1:4，图片区域获得更多显示空间

2. **图片保持原始大小**
   - 移除了自动缩放功能
   - 图片以原始尺寸显示
   - 添加滚动条支持查看大图片的所有部分
   - 小图片自动居中显示

3. **精简日志输出框**
   - 结果显示区域高度从8行减少到4行
   - 字体调整为更小的Arial 9号字体
   - 整体布局更加紧凑

4. **添加模型选择下拉框**
   - 新增模型选择功能，支持PP-OCRv5和PP-OCRv4
   - 动态模型切换，无需重启应用
   - 自动配置不同模型的路径和参数

## 🔧 具体修改内容

### 1. 界面布局重构

**之前的布局：**
```
[控制面板]  [图片显示区域]
[控制面板]  [识别结果区域]
```

**现在的布局：**
```
[控制面板]  [    图片显示区域    ]
           [    识别结果区域    ]
```

### 2. 窗口尺寸调整
- 窗口大小从 1200x800 调整为 1400x900
- 为更大的图片显示提供更多空间

### 3. 控制面板优化
- 按钮宽度从15减少到12
- 间距从5px减少到3px
- 添加了模型选择区域
- 状态显示更加紧凑

### 4. 图片显示功能增强

**新的图片显示特性：**
- ✅ 保持原始尺寸
- ✅ 支持滚动查看大图
- ✅ 小图自动居中
- ✅ 滚动条自动适应
- ✅ 重置滚动位置

### 5. 模型选择功能

**支持的模型：**
- **PP-OCRv5**（默认）：最新版本，精度更高
- **PP-OCRv4**：稳定版本，兼容性好

**模型配置：**
```python
PP-OCRv5:
- det_model_dir: "./onnxocr/models/ppocrv5/det"
- rec_model_dir: "./onnxocr/models/ppocrv5/rec"
- cls_model_dir: "./onnxocr/models/ppocrv5/cls"
- rec_char_dict_path: "./onnxocr/models/ppocrv5/ppocrv5_dict.txt"

PP-OCRv4:
- det_model_dir: "./onnxocr/models/ppocrv4/det"
- rec_model_dir: "./onnxocr/models/ppocrv4/rec"
- cls_model_dir: "./onnxocr/models/ppocrv4/cls"
```

## 🎮 新的使用体验

### 图片查看体验
1. **原始尺寸显示**：图片不再被强制缩放，保持清晰度
2. **滚动查看**：大图片可以通过滚动条查看所有部分
3. **居中显示**：小图片自动在画布中居中显示
4. **OCR结果叠加**：识别结果直接绘制在原始尺寸图片上

### 模型切换体验
1. **下拉选择**：通过下拉框轻松切换模型
2. **即时生效**：选择后立即切换，下次识别使用新模型
3. **状态提示**：显示当前使用的模型信息
4. **自动配置**：无需手动配置模型路径

### 界面操作体验
1. **紧凑布局**：控制面板更加紧凑，不占用过多空间
2. **主体突出**：图片显示区域成为界面焦点
3. **信息精简**：结果显示更加简洁，重点突出

## 📊 界面对比

| 功能 | 修改前 | 修改后 |
|------|--------|--------|
| 图片显示比例 | 1:1 | 1:4 |
| 图片尺寸 | 自动缩放 | 保持原始大小 |
| 结果框高度 | 8行 | 4行 |
| 模型选择 | 无 | 下拉框选择 |
| 控制面板 | 较宽 | 紧凑 |
| 窗口大小 | 1200x800 | 1400x900 |

## 🚀 使用建议

### 最佳实践
1. **大图片处理**：
   - 使用滚动条查看图片的不同部分
   - OCR识别后可以滚动查看所有识别结果

2. **模型选择**：
   - PP-OCRv5：推荐用于高精度要求的场景
   - PP-OCRv4：推荐用于兼容性要求高的场景

3. **界面操作**：
   - 左侧控制面板包含所有操作按钮
   - 右侧主要区域专注于图片显示和结果查看

### 性能优化
- 图片不再缩放，减少了处理时间
- 模型按需加载，提高启动速度
- 界面响应更加流畅

## 🔍 技术细节

### 图片显示算法
```python
# 保持原始大小显示
pil_image = Image.fromarray(image_array)
self.display_image = ImageTk.PhotoImage(pil_image)

# 设置滚动区域
self.canvas.configure(scrollregion=(0, 0, img_width, img_height))

# 小图片居中处理
if img_width < canvas_width and img_height < canvas_height:
    x_offset = (canvas_width - img_width) // 2
    y_offset = (canvas_height - img_height) // 2
```

### 模型动态加载
```python
def get_model_config(self):
    """根据选择的模型返回配置"""
    if self.current_model == "PP-OCRv5":
        return {...}  # PP-OCRv5配置
    elif self.current_model == "PP-OCRv4":
        return {...}  # PP-OCRv4配置
```

## 🎉 更新完成

现在的GUI界面具有以下特点：

- ✅ **图片为主体**：图片显示区域占据主要空间
- ✅ **原始尺寸**：图片保持原始大小，不失真
- ✅ **精简布局**：日志输出框更加紧凑
- ✅ **模型选择**：支持多种OCR模型切换
- ✅ **用户友好**：界面更加直观和易用

您现在可以享受更好的OCR识别体验了！🚀
