
图形用户界面应用，提供了简单易用的OCR文字识别功能。

-支支持PP-OCRv5和PP-OCRv4模型片格式（PNG, JPG, JPEG, BMP）
- 可视化显示识别结果
- 简洁直观的用户界面
## 使用方法
### 方法一：直接运行Python脚本
```bash
```
### 方法二：使用批处理文件启动（Windows）
双击 `start_gui.bat` 文件即可启动应用。
## 操作步骤
1. 点击"选择图片"按钮，选择需要识别的图片
3. 选择使用CPU或GPU进行推理
5. 识别完成后，可以查看识别结果


- PyQt5
- ONNX Runtime


- 默认使用PP-OCRv5模型，该模型支持中文、英文、数字等多种文字类型


A: 请确保已安装所有依赖项。可以运行 `pip install -r requirements.txt` 安装所需依赖。
**Q: 为什么识别结果不准确？**  

A: 在界面上选择"GPU"选项，并确保已安装ONNX Runtime GPU版本。
## 许可证
与