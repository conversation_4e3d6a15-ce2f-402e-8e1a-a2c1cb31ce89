import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from PIL import Image, ImageTk


class OCRGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ONNX OCR 文本识别工具")
        self.root.geometry("800x600")

        self.select_btn = tk.Button(root, text="选择图像", command=self.select_image)
        self.select_btn.pack(pady=20)

        self.status_var = tk.StringVar(value="就绪")
        self.status_bar = ttk.Label(
            root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        self.input_image_path = None

    def select_image(self):
        """选择输入图像"""
        filetypes = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.gif"),
            ("所有文件", "*.*"),
        ]
        filepath = filedialog.askopenfilename(title="选择图像文件", filetypes=filetypes)

        if filepath:
            self.input_image_path = filepath
            self.status_var.set(f"已选择图像: {os.path.basename(filepath)}")
            messagebox.showinfo("信息", f"已选择图像：{filepath}")


def main():
    root = tk.Tk()
    app = OCRGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
