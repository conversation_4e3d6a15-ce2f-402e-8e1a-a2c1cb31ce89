#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR GUI应用 - 基于tkinter的图片OCR识别界面
支持图片选择、加载、OCR识别和结果显示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np
import os
import threading
import time
from onnxocr.onnx_paddleocr import ONNXPaddleOcr
from onnxocr.utils import draw_ocr


class OCRGui:
    def __init__(self, root):
        self.root = root
        self.root.title("OnnxOCR - 图片文字识别工具")
        self.root.geometry("1200x800")

        # 初始化变量
        self.original_image = None
        self.display_image = None
        self.ocr_result = None
        self.ocr_model = None
        self.is_processing = False

        # 创建界面
        self.create_widgets()

        # 初始化OCR模型
        self.init_ocr_model()

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(
            row=0, column=0, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10)
        )

        # 图片选择按钮
        ttk.Button(
            control_frame, text="选择图片", command=self.select_image, width=15
        ).grid(row=0, column=0, pady=5)

        # OCR识别按钮
        self.ocr_button = ttk.Button(
            control_frame,
            text="开始识别",
            command=self.start_ocr,
            width=15,
            state="disabled",
        )
        self.ocr_button.grid(row=1, column=0, pady=5)

        # 保存结果按钮
        self.save_button = ttk.Button(
            control_frame,
            text="保存结果",
            command=self.save_result,
            width=15,
            state="disabled",
        )
        self.save_button.grid(row=2, column=0, pady=5)

        # 清除按钮
        ttk.Button(control_frame, text="清除", command=self.clear_all, width=15).grid(
            row=3, column=0, pady=5
        )

        # 分隔线
        ttk.Separator(control_frame, orient="horizontal").grid(
            row=4, column=0, sticky=(tk.W, tk.E), pady=10
        )

        # 设置选项
        settings_frame = ttk.LabelFrame(control_frame, text="设置选项", padding="5")
        settings_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=5)

        # GPU选项
        self.use_gpu_var = tk.BooleanVar()
        ttk.Checkbutton(settings_frame, text="使用GPU", variable=self.use_gpu_var).grid(
            row=0, column=0, sticky=tk.W
        )

        # 角度分类选项
        self.use_angle_cls_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            settings_frame, text="角度分类", variable=self.use_angle_cls_var
        ).grid(row=1, column=0, sticky=tk.W)

        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(control_frame, text="状态:").grid(
            row=6, column=0, sticky=tk.W, pady=(10, 0)
        )
        self.status_label = ttk.Label(
            control_frame, textvariable=self.progress_var, foreground="blue"
        )
        self.status_label.grid(row=7, column=0, sticky=tk.W)

        # 进度条
        self.progress_bar = ttk.Progressbar(control_frame, mode="indeterminate")
        self.progress_bar.grid(row=8, column=0, sticky=(tk.W, tk.E), pady=5)

        # 右侧图片显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图片显示", padding="10")
        image_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)

        # 图片显示画布
        self.canvas = tk.Canvas(
            image_frame, bg="white", relief=tk.SUNKEN, borderwidth=2
        )
        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        v_scrollbar = ttk.Scrollbar(
            image_frame, orient=tk.VERTICAL, command=self.canvas.yview
        )
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.canvas.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(
            image_frame, orient=tk.HORIZONTAL, command=self.canvas.xview
        )
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.canvas.configure(xscrollcommand=h_scrollbar.set)

        # 底部识别结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="识别结果", padding="10")
        result_frame.grid(
            row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0)
        )
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

        # 结果文本框
        self.result_text = tk.Text(result_frame, height=8, wrap=tk.WORD)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 结果文本框滚动条
        result_scrollbar = ttk.Scrollbar(
            result_frame, orient=tk.VERTICAL, command=self.result_text.yview
        )
        result_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.result_text.configure(yscrollcommand=result_scrollbar.set)

    def init_ocr_model(self):
        """初始化OCR模型"""
        try:
            self.progress_var.set("正在初始化OCR模型...")
            self.progress_bar.start()

            # 在后台线程中初始化模型
            def init_model():
                try:
                    self.ocr_model = ONNXPaddleOcr(use_angle_cls=True, use_gpu=False)
                    self.root.after(
                        0, lambda: self.progress_var.set("OCR模型初始化完成")
                    )
                    self.root.after(0, self.progress_bar.stop)
                except Exception as e:
                    self.root.after(
                        0, lambda: self.progress_var.set(f"模型初始化失败: {str(e)}")
                    )
                    self.root.after(0, self.progress_bar.stop)

            threading.Thread(target=init_model, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"初始化OCR模型失败: {str(e)}")
            self.progress_var.set("模型初始化失败")
            self.progress_bar.stop()

    def select_image(self):
        """选择图片文件"""
        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*"),
        ]

        file_path = filedialog.askopenfilename(
            title="选择图片文件", filetypes=file_types
        )

        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path):
        """加载并显示图片"""
        try:
            # 使用OpenCV读取图片
            self.original_image = cv2.imread(file_path)
            if self.original_image is None:
                messagebox.showerror("错误", "无法读取图片文件")
                return

            # 转换为RGB格式用于显示
            image_rgb = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)

            # 调整图片大小以适应显示
            self.display_image_on_canvas(image_rgb)

            # 启用OCR按钮
            self.ocr_button.config(state="normal")
            self.progress_var.set(f"图片已加载: {os.path.basename(file_path)}")

            # 清除之前的识别结果
            self.result_text.delete(1.0, tk.END)
            self.ocr_result = None
            self.save_button.config(state="disabled")

        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")

    def display_image_on_canvas(self, image_array):
        """在画布上显示图片"""
        # 获取画布大小
        self.canvas.update()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # 如果画布还没有初始化，使用默认大小
        if canvas_width <= 1:
            canvas_width = 600
        if canvas_height <= 1:
            canvas_height = 400

        # 计算缩放比例
        img_height, img_width = image_array.shape[:2]
        scale_w = canvas_width / img_width
        scale_h = canvas_height / img_height
        scale = min(scale_w, scale_h, 1.0)  # 不放大，只缩小

        # 调整图片大小
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)

        # 转换为PIL图片
        pil_image = Image.fromarray(image_array)
        pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为tkinter可用的格式
        self.display_image = ImageTk.PhotoImage(pil_image)

        # 清除画布并显示图片
        self.canvas.delete("all")
        self.canvas.create_image(
            canvas_width // 2,
            canvas_height // 2,
            anchor=tk.CENTER,
            image=self.display_image,
        )

        # 更新滚动区域
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def start_ocr(self):
        """开始OCR识别"""
        if self.original_image is None:
            messagebox.showwarning("警告", "请先选择图片")
            return

        if self.ocr_model is None:
            messagebox.showwarning("警告", "OCR模型未初始化完成")
            return

        if self.is_processing:
            messagebox.showinfo("提示", "正在处理中，请稍候...")
            return

        # 禁用按钮，防止重复点击
        self.ocr_button.config(state="disabled")
        self.is_processing = True
        self.progress_var.set("正在进行OCR识别...")
        self.progress_bar.start()

        # 在后台线程中执行OCR
        def ocr_worker():
            try:
                # 根据设置更新模型参数
                use_gpu = self.use_gpu_var.get()
                use_angle_cls = self.use_angle_cls_var.get()

                # 如果设置发生变化，重新初始化模型
                if (
                    hasattr(self.ocr_model, "use_gpu")
                    and self.ocr_model.use_gpu != use_gpu
                ) or (
                    hasattr(self.ocr_model, "use_angle_cls")
                    and self.ocr_model.use_angle_cls != use_angle_cls
                ):
                    self.root.after(
                        0, lambda: self.progress_var.set("正在更新模型设置...")
                    )
                    self.ocr_model = ONNXPaddleOcr(
                        use_angle_cls=use_angle_cls, use_gpu=use_gpu
                    )

                # 执行OCR识别
                start_time = time.time()
                result = self.ocr_model.ocr(self.original_image)
                end_time = time.time()

                # 在主线程中更新UI
                self.root.after(
                    0, lambda: self.ocr_completed(result, end_time - start_time)
                )

            except Exception as e:
                self.root.after(0, lambda: self.ocr_error(str(e)))

        threading.Thread(target=ocr_worker, daemon=True).start()

    def ocr_completed(self, result, processing_time):
        """OCR识别完成的回调"""
        try:
            self.ocr_result = result
            self.is_processing = False
            self.progress_bar.stop()
            self.ocr_button.config(state="normal")

            # 显示识别结果
            self.display_ocr_result(result, processing_time)

            # 在图片上绘制识别结果
            self.draw_ocr_result(result)

            # 启用保存按钮
            self.save_button.config(state="normal")

            self.progress_var.set(f"识别完成，耗时: {processing_time:.2f}秒")

        except Exception as e:
            self.ocr_error(f"处理识别结果时出错: {str(e)}")

    def ocr_error(self, error_msg):
        """OCR识别出错的回调"""
        self.is_processing = False
        self.progress_bar.stop()
        self.ocr_button.config(state="normal")
        self.progress_var.set("识别失败")
        messagebox.showerror("错误", f"OCR识别失败: {error_msg}")

    def display_ocr_result(self, result, processing_time):
        """显示OCR识别结果"""
        self.result_text.delete(1.0, tk.END)

        if not result or not result[0]:
            self.result_text.insert(tk.END, "未识别到文字内容")
            return

        # 添加处理时间信息
        self.result_text.insert(tk.END, f"识别耗时: {processing_time:.2f}秒\n")
        self.result_text.insert(tk.END, f"识别到 {len(result[0])} 个文本区域\n")
        self.result_text.insert(tk.END, "-" * 50 + "\n\n")

        # 显示识别结果
        for i, line in enumerate(result[0]):
            bbox = line[0]  # 边界框坐标
            text_info = line[1]  # 文本和置信度
            text = text_info[0]
            confidence = text_info[1]

            self.result_text.insert(tk.END, f"区域 {i + 1}:\n")
            self.result_text.insert(tk.END, f"文本: {text}\n")
            self.result_text.insert(tk.END, f"置信度: {confidence:.4f}\n")
            self.result_text.insert(tk.END, f"坐标: {bbox}\n")
            self.result_text.insert(tk.END, "\n")

    def draw_ocr_result(self, result):
        """在图片上绘制OCR识别结果"""
        if not result or not result[0]:
            return

        try:
            # 复制原始图片
            image_with_boxes = self.original_image.copy()

            # 提取识别结果
            boxes = [line[0] for line in result[0]]
            texts = [line[1][0] for line in result[0]]
            scores = [line[1][1] for line in result[0]]

            # 转换为RGB格式
            image_rgb = cv2.cvtColor(image_with_boxes, cv2.COLOR_BGR2RGB)

            # 使用draw_ocr函数绘制结果
            result_image = draw_ocr(image_rgb, boxes, texts, scores)

            # 显示带有识别结果的图片
            self.display_image_on_canvas(result_image)

        except Exception as e:
            print(f"绘制OCR结果时出错: {str(e)}")

    def save_result(self):
        """保存识别结果"""
        if self.ocr_result is None:
            messagebox.showwarning("警告", "没有识别结果可保存")
            return

        # 选择保存位置
        file_path = filedialog.asksaveasfilename(
            title="保存识别结果",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
        )

        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write("OCR识别结果\n")
                    f.write("=" * 50 + "\n\n")

                    for i, line in enumerate(self.ocr_result[0]):
                        text = line[1][0]
                        confidence = line[1][1]
                        bbox = line[0]

                        f.write(f"区域 {i + 1}:\n")
                        f.write(f"文本: {text}\n")
                        f.write(f"置信度: {confidence:.4f}\n")
                        f.write(f"坐标: {bbox}\n")
                        f.write("\n")

                messagebox.showinfo("成功", f"识别结果已保存到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存文件失败: {str(e)}")

    def clear_all(self):
        """清除所有内容"""
        self.original_image = None
        self.display_image = None
        self.ocr_result = None

        # 清除画布
        self.canvas.delete("all")

        # 清除结果文本
        self.result_text.delete(1.0, tk.END)

        # 重置按钮状态
        self.ocr_button.config(state="disabled")
        self.save_button.config(state="disabled")

        # 重置状态
        self.progress_var.set("就绪")
        self.is_processing = False
        self.progress_bar.stop()


def main():
    """主函数"""
    root = tk.Tk()
    app = OCRGui(root)

    # 设置窗口图标（如果有的话）
    try:
        # 可以设置窗口图标
        # root.iconbitmap('icon.ico')
        pass
    except:
        pass

    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
