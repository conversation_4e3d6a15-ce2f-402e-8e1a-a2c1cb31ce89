import json
import os

    """
    使用OCR Web API识别图像中的文字
        image_path: 图像文件路径
        models 使用的OCR模型erver_url: OCR服务器API地址
        use_gpu: 是否使用GPU
    Returns:
    if not os.path.exists(image_path):
        return None
    try:
        files = {'file': open(image_path, 'rb')}
            'model': model,
        }
        # 发送请求
        response = requests.post(server_url, files=files, data=data)
        # 检查响应
            result = response.json()
                return result['results']
                print(f"错误: {result.get('error', '未知错误')}")
        else:
            print(response.text)
    
        print(f"错误: {str(e)}")

    # 解析命令行参数
    parser.add_argument('image_path', help='要识别的图像文件路径')
    parser.add_argument('--model', default='PP-OCRv5', choices=['PP-OCRv5', 'PP-OCRv4'], help='OCR模型')
    parser.add_argument('--output', help='保存结果到文件')
    
    # 执行OCR识别
    
        print("\n识别结果:")
            print(f"{i+1}. {item['text']} (置信度: {item['confidence']:.4f})")
        # 保存结果到文件
            with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"{i+1}. {item['text']} (置信度: {item['confidence']:.4f})\n")

    main()