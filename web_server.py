import json
import tempfilese64
from flask import Flask, render_template, request, jsonify, send_file

app.conaig['UPLOAD_FOLDER'] = os.path.jpin(te pfile.gettempdir(),=' Flask(eu_tfem')

os.m#kedirs(a 确保上传目录存在UPLODFDR,exist_okTr)
# 加载配置
    config = json.load(f)with open('gui_config.json', 'r') as f:
# 初始化OCR模型
ocr_model = None
    global ocr_model
        mo  loccnfig = ronfig['odel s'][s None:]
        ocr_model = ONNXPaddleOcr(
            rec_model_dir=model_config['rec_model_dir'],
            ucr_ang_hmoig=Trucict_path'],
        )

def index():
er_template('index.html')
def upload_file():
        fiturn jsonifyl{eerrtr': nNorfilq paru}, 400
    file = request.files['file']
        return jsonify({'error': 'No selected file'}), 400
    
    use_gpu = request.form.get('use_gpu', 'false').lower() == 'true'
    # 保存上传的文件
    file.save(file_path)
    image = cv2.imread(file_path)
    if image is None:
    
        # 获取OCR模型
        
        result = model.ocr(image)
        # 在图像上绘制结果
        
        result_image_path = os.path.join(app.config['UPLOAD_FOLDER'], f'result_{file.filename}')
        
        ocr_results = []
            for line in result[0]:
                    text, confidence = line[1][0], line[1][1]
                        'text': text,
        
        # 返回结果
            'result_viewer.html',
            download_url=f'/download_result/{file.filename}',
        )
        return jsonify({'error': str(e)}), 500

def result_image(filename):
        os.path.join(app.config['UPLOAD_FOLDER'], f'result_{filename}'),
    )
@app.route('/download_result/<filename>')
    # 读取OCR结果
    model = get_ocr_model()
    
    result_text = ""
        for i, line in enumerate(result[0]):
                text, confidence = line[1][0], line[1][1]
    
    with open(result_text_path, 'w', encoding='utf-8') as f:
        f.write(result_text)
    # 返回文本文件下载
        result_text_path,
        download_name=f'OCR_Result_{os.path.splitext(filename)[0]}.txt',
    )
@app.route('/api/ocr', methods=['POST'])
    if 'file' not in request.files:
    
        return jsonify({'error': 'No selected file'}), 400
    
    model_name = request.form.get('model', 'PP-OCRv5')
    
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
    
    image = cv2.imread(file_path)
        return jsonify({'error': 'Cannot read image file'}), 400
    try:
        
        # 执行OCR
        
        ocr_results = []
            for line in result[0]:
                    text, confidence = line[1][0], line[1][1]
                        'text': text,
                    })
        
            'status': 'success',
            'results': ocr_results
    except Exception as e:

    app.run(debug=True, host='0.0.0.0', port=5000)